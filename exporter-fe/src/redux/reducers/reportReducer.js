import * as types from '../actions/types';

// Initial state
const initialState = {
  reports: [],
  activeReportId: null,
  isLoading: false,
  error: null
};

/**
 * Report reducer
 * @param {Object} state - Current state
 * @param {Object} action - Action object
 * @returns {Object} New state
 */
const reportReducer = (state = initialState, action) => {
  switch (action.type) {
    case types.CREATE_REPORT: {
      const newReport = action.payload;
      // When creating a new report, don't automatically set it as active
      // This allows us to control when a report becomes active
      return {
        ...state,
        reports: [...state.reports, newReport]
        // Removed activeReportId setting to allow explicit control
      };
    }

    case types.UPDATE_REPORT: {
      const updatedReport = action.payload;
      return {
        ...state,
        reports: state.reports.map(report =>
          report.id === updatedReport.id ? updatedReport : report
        )
      };
    }

    case types.DELETE_REPORT: {
      const reportId = action.payload;
      const filteredReports = state.reports.filter(report => report.id !== reportId);
      return {
        ...state,
        reports: filteredReports,
        activeReportId: filteredReports.length > 0 ? filteredReports[0].id : null
      };
    }

    case types.SET_ACTIVE_REPORT: {
      return {
        ...state,
        activeReportId: action.payload
      };
    }

    case types.ADD_PAGE: {
      const { reportId, page, afterPageIndex } = action.payload;
      return {
        ...state,
        reports: state.reports.map(report => {
          if (report.id === reportId) {
            // If afterPageIndex is provided, insert the page after that index
            // Otherwise, add it to the end
            let newPages;
            let newActivePageIndex;

            if (afterPageIndex !== undefined && afterPageIndex >= 0 && afterPageIndex < report.pages.length) {
              // Insert the page after the specified index
              newPages = [
                ...report.pages.slice(0, afterPageIndex + 1),
                page,
                ...report.pages.slice(afterPageIndex + 1)
              ];
              newActivePageIndex = afterPageIndex + 1;
            } else {
              // Add to the end
              newPages = [...report.pages, page];
              newActivePageIndex = newPages.length - 1;
            }

            return {
              ...report,
              pages: newPages,
              activePageIndex: newActivePageIndex,
              updatedAt: new Date().toISOString()
            };
          }
          return report;
        })
      };
    }

    case types.UPDATE_PAGE: {
      const { reportId, pageId, updates } = action.payload;
      return {
        ...state,
        reports: state.reports.map(report => {
          if (report.id === reportId) {
            return {
              ...report,
              pages: report.pages.map(page =>
                page.id === pageId ? { ...page, ...updates } : page
              ),
              updatedAt: new Date().toISOString()
            };
          }
          return report;
        })
      };
    }

    case types.DELETE_PAGE: {
      const { reportId, pageId } = action.payload;
      return {
        ...state,
        reports: state.reports.map(report => {
          if (report.id === reportId) {
            // Prevent deleting the last page
            if (report.pages.length <= 1) {
              return report;
            }

            const newPages = report.pages.filter(page => page.id !== pageId);
            return {
              ...report,
              pages: newPages,
              activePageIndex: Math.min(report.activePageIndex, newPages.length - 1),
              updatedAt: new Date().toISOString()
            };
          }
          return report;
        })
      };
    }

    case types.SET_ACTIVE_PAGE: {
      const { reportId, pageIndex } = action.payload;
      return {
        ...state,
        reports: state.reports.map(report =>
          report.id === reportId ? { ...report, activePageIndex: pageIndex } : report
        )
      };
    }

    case types.ADD_COMPONENT: {
      const { reportId, pageId, component } = action.payload;
      console.log('Reducer - ADD_COMPONENT:', { reportId, pageId, component });
      const newState = {
        ...state,
        reports: state.reports.map(report => {
          if (report.id === reportId) {
            return {
              ...report,
              pages: report.pages.map(page => {
                if (page.id === pageId) {
                  const updatedPage = {
                    ...page,
                    components: [...page.components, component],
                    selectedComponentId: component.id // Select the newly added component
                  };
                  console.log('Reducer - Updated page:', updatedPage);
                  return updatedPage;
                }
                return page;
              }),
              updatedAt: new Date().toISOString()
            };
          }
          return report;
        })
      };
      console.log('Reducer - New state:', newState);
      return newState;
    }

    case types.UPDATE_COMPONENT: {
      const { reportId, pageId, componentId, updates } = action.payload;
      return {
        ...state,
        reports: state.reports.map(report => {
          if (report.id === reportId) {
            return {
              ...report,
              pages: report.pages.map(page => {
                if (page.id === pageId) {
                  return {
                    ...page,
                    components: page.components.map(component =>
                      component.id === componentId ? { ...component, ...updates } : component
                    )
                  };
                }
                return page;
              }),
              updatedAt: new Date().toISOString()
            };
          }
          return report;
        })
      };
    }

    case types.DELETE_COMPONENT: {
      const { reportId, pageId, componentId } = action.payload;
      return {
        ...state,
        reports: state.reports.map(report => {
          if (report.id === reportId) {
            return {
              ...report,
              pages: report.pages.map(page => {
                if (page.id === pageId) {
                  return {
                    ...page,
                    components: page.components.filter(component => component.id !== componentId),
                    selectedComponentId: page.selectedComponentId === componentId ? null : page.selectedComponentId
                  };
                }
                return page;
              }),
              updatedAt: new Date().toISOString()
            };
          }
          return report;
        })
      };
    }

    case types.SELECT_COMPONENT: {
      const { reportId, pageId, componentId } = action.payload;
      console.log('Reducer - SELECT_COMPONENT:', { reportId, pageId, componentId });
      const newState = {
        ...state,
        reports: state.reports.map(report => {
          if (report.id === reportId) {
            return {
              ...report,
              pages: report.pages.map(page => {
                if (page.id === pageId) {
                  const updatedPage = {
                    ...page,
                    selectedComponentId: componentId
                  };
                  console.log('Reducer - Updated page with selectedComponentId:', updatedPage.selectedComponentId);
                  return updatedPage;
                }
                return page;
              })
            };
          }
          return report;
        })
      };
      console.log('Reducer - New state after SELECT_COMPONENT:', newState);
      return newState;
    }

    case types.SET_LOADING: {
      return {
        ...state,
        isLoading: action.payload
      };
    }

    case types.SET_ERROR: {
      return {
        ...state,
        error: action.payload
      };
    }

    case types.LOAD_REPORTS: {
      // Load reports without setting an active report
      return {
        ...state,
        reports: action.payload,
        activeReportId: null // Explicitly set to null to ensure we start with no active report
      };
    }

    default:
      return state;
  }
};

export default reportReducer;

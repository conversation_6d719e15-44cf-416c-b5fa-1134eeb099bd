.settings-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.settings-debug-info {
  background-color: #f0f0f0;
  padding: 8px;
  margin-bottom: 8px;
  font-size: 11px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-family: monospace;
}

.settings-indicator {
  padding: 8px;
  margin-bottom: 10px;
  font-size: 12px;
  border-radius: 4px;
  font-weight: bold;
  text-align: center;
}

.settings-indicator.element {
  background-color: #e8f5e8;
  border: 2px solid #4caf50;
  color: #2e7d32;
}

.settings-indicator.page {
  background-color: #f0f8ff;
  border: 2px solid #2196f3;
  color: #1565c0;
}

.settings-content {
  flex: 1;
  overflow-y: auto;
}

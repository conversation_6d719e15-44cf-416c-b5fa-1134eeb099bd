import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import PageSettings from '../page/PageSettings';
import ElementSettings from '../page/ElementSettings';
import './Settings.css';

/**
 * Settings component that manages the display of either PageSettings or ElementSettings
 * based on the currently selected component
 */
const Settings = ({ activePage, onPageUpdate, onComponentUpdate, onDebugInfoChange }) => {
  const [renderCount, setRenderCount] = useState(0);
  const [lastStateChange, setLastStateChange] = useState('initial');

  // Direct Redux selector to get the selected component ID
  const selectedComponentId = useSelector(state => {
    const reports = state.report.reports;
    const activeReportId = state.report.activeReportId;
    const activeReport = reports.find(report => report.id === activeReportId);
    const activePage = activeReport ? activeReport.pages[activeReport.activePageIndex] : null;
    return activePage?.selectedComponentId || null;
  });

  // Find the selected component
  const selectedComponent = selectedComponentId && activePage
    ? activePage.components.find(comp => comp.id === selectedComponentId)
    : null;

  // Track renders
  useEffect(() => {
    setRenderCount(prev => prev + 1);
  });

  // Track state changes
  useEffect(() => {
    const timestamp = new Date().toLocaleTimeString();
    if (selectedComponentId) {
      setLastStateChange(`Component ${selectedComponentId} selected at ${timestamp}`);
    } else {
      setLastStateChange(`No component selected at ${timestamp}`);
    }
  }, [selectedComponentId]);

  // Send debug info to parent (for debug panel)
  useEffect(() => {
    if (onDebugInfoChange) {
      onDebugInfoChange({
        renderCount,
        lastStateChange,
        selectedComponentId,
        componentFound: !!selectedComponent,
        willRender: selectedComponent ? 'ElementSettings' : 'PageSettings'
      });
    }
  }, [renderCount, lastStateChange, selectedComponentId, selectedComponent, onDebugInfoChange]);

  // Handle component updates by passing the componentId to the parent
  const handleComponentUpdate = (updates) => {
    if (selectedComponent) {
      onComponentUpdate(selectedComponent.id, updates);
    }
  };

  return (
    <div className="settings-container">
      {selectedComponent ? (
        <ElementSettings
          key={selectedComponent.id} // Force re-render when component changes
          component={selectedComponent}
          onUpdate={handleComponentUpdate}
        />
      ) : (
        <PageSettings
          key={activePage?.id || 'no-page'} // Force re-render when page changes
          page={activePage}
          onUpdate={onPageUpdate}
        />
      )}
    </div>
  );
};

Settings.propTypes = {
  activePage: PropTypes.object,
  onPageUpdate: PropTypes.func.isRequired,
  onComponentUpdate: PropTypes.func.isRequired,
  onDebugInfoChange: PropTypes.func
};

Settings.defaultProps = {
  activePage: null
};

export default Settings;

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import PageSettings from '../page/PageSettings';
import ElementSettings from '../page/ElementSettings';
import './Settings.css';

/**
 * Settings component that manages the display of either PageSettings or ElementSettings
 * based on the currently selected component
 */
const Settings = ({ activePage, onPageUpdate, onComponentUpdate }) => {
  const [renderCount, setRenderCount] = useState(0);
  const [lastStateChange, setLastStateChange] = useState('initial');

  // Direct Redux selector to get the selected component ID
  const selectedComponentId = useSelector(state => {
    const reports = state.report.reports;
    const activeReportId = state.report.activeReportId;
    const activeReport = reports.find(report => report.id === activeReportId);
    const activePage = activeReport ? activeReport.pages[activeReport.activePageIndex] : null;
    return activePage?.selectedComponentId || null;
  });

  // Find the selected component
  const selectedComponent = selectedComponentId && activePage
    ? activePage.components.find(comp => comp.id === selectedComponentId)
    : null;

  // Track renders
  useEffect(() => {
    setRenderCount(prev => prev + 1);
  });

  // Track state changes
  useEffect(() => {
    const timestamp = new Date().toLocaleTimeString();
    if (selectedComponentId) {
      setLastStateChange(`Component ${selectedComponentId} selected at ${timestamp}`);
    } else {
      setLastStateChange(`No component selected at ${timestamp}`);
    }
  }, [selectedComponentId]);

  console.log('⚙️ Settings component RENDER - selectedComponentId:', selectedComponentId);
  console.log('⚙️ Settings component RENDER - selectedComponent:', selectedComponent?.id);
  console.log('⚙️ Settings component RENDER - will show:', selectedComponent ? 'ElementSettings' : 'PageSettings');

  // Handle component updates by passing the componentId to the parent
  const handleComponentUpdate = (updates) => {
    if (selectedComponent) {
      onComponentUpdate(selectedComponent.id, updates);
    }
  };

  return (
    <div className="settings-container">
      {/* Debug info */}
      <div className="settings-debug-info">
        <strong>⚙️ Settings Debug:</strong><br/>
        Render count: {renderCount}<br/>
        Last change: {lastStateChange}<br/>
        Selected ID: {selectedComponentId || 'null'}<br/>
        Component found: {selectedComponent ? selectedComponent.id : 'null'}<br/>
        Will render: {selectedComponent ? 'ElementSettings' : 'PageSettings'}
      </div>

      {/* Rendering indicator */}
      <div className={`settings-indicator ${selectedComponent ? 'element' : 'page'}`}>
        <strong>
          {selectedComponent ? '🔧 Element Settings' : '📄 Page Settings'}
        </strong>
        {selectedComponent && <span> - {selectedComponent.type} ({selectedComponent.id})</span>}
      </div>

      {/* Actual settings component */}
      <div className="settings-content">
        {selectedComponent ? (
          <ElementSettings
            key={selectedComponent.id} // Force re-render when component changes
            component={selectedComponent}
            onUpdate={handleComponentUpdate}
          />
        ) : (
          <PageSettings
            key={activePage?.id || 'no-page'} // Force re-render when page changes
            page={activePage}
            onUpdate={onPageUpdate}
          />
        )}
      </div>
    </div>
  );
};

Settings.propTypes = {
  activePage: PropTypes.object,
  onPageUpdate: PropTypes.func.isRequired,
  onComponentUpdate: PropTypes.func.isRequired
};

Settings.defaultProps = {
  activePage: null
};

export default Settings;

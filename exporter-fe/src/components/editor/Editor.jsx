import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ReportHeader from '../report/ReportHeader';
import Report from '../report/Report';
import PageSettings from '../page/PageSettings';
import ElementSettings from '../page/ElementSettings';
import DebugPanel from '../debug/DebugPanel';
import { useReport } from '../../hooks/useReport';
import { createTextComponent, createImageComponent } from '../../models/ComponentTypes';
import { generateReportUrl } from '../../utils/urlUtils';
import { useSelector } from 'react-redux';
import './Editor.css';

/**
 * Main editor component that combines all parts
 */
const Editor = () => {
  const navigate = useNavigate();
  const user = useSelector(state => state.auth.user);

  const {
    activeReport,
    activePage,
    updatePage,
    updateComponent,
    selectComponent,
    createNewReport,
    addComponent
  } = useReport();

  // State to track what's currently active for the right panel
  const [activeSelection, setActiveSelection] = useState({ type: 'page', componentId: null });

  // Get the selected component
  const selectedComponent = activePage && activePage.selectedComponentId
    ? activePage.components.find(comp => comp.id === activePage.selectedComponentId)
    : null;

  // Update active selection when selectedComponentId changes
  useEffect(() => {
    if (activePage?.selectedComponentId) {
      console.log('Setting active selection to component:', activePage.selectedComponentId);
      setActiveSelection({ type: 'component', componentId: activePage.selectedComponentId });
    } else {
      console.log('Setting active selection to page');
      setActiveSelection({ type: 'page', componentId: null });
    }
  }, [activePage?.selectedComponentId]);

  // Debug logging
  useEffect(() => {
    console.log('Editor - activeSelection:', activeSelection);
    console.log('Editor - selectedComponent:', selectedComponent);
    console.log('Editor - activePage.selectedComponentId:', activePage?.selectedComponentId);
  }, [activeSelection, selectedComponent, activePage?.selectedComponentId]);

  // Track if URL has been set for this report to prevent unnecessary updates
  const urlSetRef = useRef(null);

  // Update URL when active report changes (but not on every report update)
  useEffect(() => {
    if (activeReport && user) {
      const reportUrl = generateReportUrl(user.uid, activeReport.name, activeReport.id);

      // Only update URL if it's a different report or if URL hasn't been set yet
      if (urlSetRef.current !== activeReport.id) {
        console.log('Setting URL for report:', activeReport.id);
        window.history.replaceState(null, '', reportUrl);
        urlSetRef.current = activeReport.id;
      }
    }
  }, [activeReport?.id, user]); // Only depend on report ID and user, not the entire report object

  // Handle page updates
  const handlePageUpdate = (updates) => {
    if (activeReport && activePage) {
      updatePage(activeReport.id, activePage.id, updates);
    }
  };

  // Handle component update
  const handleComponentUpdate = (updates) => {
    if (activeReport && activePage && selectedComponent) {
      updateComponent(activeReport.id, activePage.id, selectedComponent.id, updates);
    }
  };

  // Handle creating a new report
  const handleCreateReport = async () => {
    try {
      // Use the new parameter to automatically set the report as active
      const newReport = await createNewReport('Untitled Report', true);

      // Navigate to the new report URL
      if (newReport && user) {
        const reportUrl = generateReportUrl(user.uid, newReport.name, newReport.id);
        navigate(reportUrl);
      }
    } catch (error) {
      console.error('Error creating new report:', error);
    }
  };

  // Handle adding a text component
  const handleAddTextComponent = () => {
    if (activeReport && activePage) {
      // Create a text component at the center of the visible area
      const textComponent = createTextComponent(
        `text-${Date.now()}`,
        activePage.margins.left + 20, // X position (20mm from left margin)
        activePage.margins.top + 20    // Y position (20mm from top margin)
      );

      console.log('Adding text component:', textComponent);
      addComponent(activeReport.id, activePage.id, textComponent);
      console.log('After adding component, activePage:', activePage);
    }
  };

  // Handle adding an image component
  const handleAddImageComponent = () => {
    if (activeReport && activePage) {
      // Create an image component at the center of the visible area
      const imageComponent = createImageComponent(
        `image-${Date.now()}`,
        activePage.margins.left + 20, // X position (20mm from left margin)
        activePage.margins.top + 50    // Y position (50mm from top margin)
      );

      addComponent(activeReport.id, activePage.id, imageComponent);
    }
  };

  // Handle debug panel actions
  const handleDebugSelectComponent = (componentId) => {
    if (activeReport && activePage) {
      selectComponent(activeReport.id, activePage.id, componentId);
    }
  };

  const handleDebugDeselectComponent = () => {
    if (activeReport && activePage) {
      selectComponent(activeReport.id, activePage.id, null);
    }
  };

  return (
    <>
      {/* Debug Panel - Rendered outside app-layout to avoid overflow issues */}
      <DebugPanel
        activeReport={activeReport}
        activePage={activePage}
        selectedComponent={selectedComponent}
        onSelectComponent={handleDebugSelectComponent}
        onDeselectComponent={handleDebugDeselectComponent}
      />

      <div className="app-layout">
        {/* Main editor area */}
        <div className="editor-container">
        <ReportHeader />

        {activeReport ? (
          <div className="editor-main">
            <div className="editor-toolbar">
              <div className="tool-group">
                <button
                  className="tool-button"
                  onClick={handleAddTextComponent}
                  disabled={!activePage}
                >
                  Add Text
                </button>
                <button
                  className="tool-button"
                  onClick={handleAddImageComponent}
                  disabled={!activePage}
                >
                  Add Image
                </button>
              </div>
            </div>

            <div className="editor-content">
              <Report />
            </div>

            <div className="editor-sidebar">
              {activePage ? (
                <div className="sidebar-content">
                  {/* Debug info */}
                  <div style={{
                    backgroundColor: '#f0f0f0',
                    padding: '10px',
                    marginBottom: '10px',
                    fontSize: '12px',
                    border: '1px solid #ccc'
                  }}>
                    <strong>Debug Info:</strong><br/>
                    selectedComponentId: {activePage?.selectedComponentId || 'null'}<br/>
                    selectedComponent: {selectedComponent ? selectedComponent.id : 'null'}<br/>
                    components count: {activePage?.components?.length || 0}<br/>
                    Should show: {selectedComponent ? 'ElementSettings' : 'PageSettings'}
                  </div>

                  {(() => {
                    console.log('🎯 Sidebar render - selectedComponent:', selectedComponent);
                    console.log('🎯 Sidebar render - selectedComponent truthy?', !!selectedComponent);
                    console.log('🎯 Sidebar render - Will render:', selectedComponent ? 'ElementSettings' : 'PageSettings');

                    if (selectedComponent) {
                      console.log('🔧 About to render ElementSettings with component:', selectedComponent.id);
                      return (
                        <ElementSettings
                          component={selectedComponent}
                          onUpdate={handleComponentUpdate}
                        />
                      );
                    } else {
                      console.log('📄 About to render PageSettings with page:', activePage?.id);
                      return (
                        <PageSettings
                          page={activePage}
                          onUpdate={handlePageUpdate}
                        />
                      );
                    }
                  })()}
                </div>
              ) : (
                <div className="no-page-selected">
                  <p>No page selected</p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="no-report-selected">
            <div className="welcome-screen">
              <h1>Welcome to Exporter</h1>
              <p>Create beautiful reports with customizable pages and export them easily.</p>
              <div className="welcome-actions">
                <button
                  className="welcome-button"
                  onClick={handleCreateReport}
                >
                  Create New Report
                </button>
              </div>
            </div>
          </div>
        )}
        </div>
      </div>
    </>
  );
};

export default Editor;

import React from 'react';
import PropTypes from 'prop-types';
import { PAGE_SIZES, ORIENTATIONS } from '../../models/ReportTypes';
import './PageSettings.css';

/**
 * Component for editing page settings
 */
const PageSettings = ({ page, onUpdate }) => {
  console.log('📄 PageSettings RENDERED with page:', page?.id);
  // Handle page size change
  const handlePageSizeChange = (e) => {
    const newSize = e.target.value;
    
    // If switching to custom, initialize with current dimensions
    if (newSize === 'custom') {
      const currentSize = PAGE_SIZES[page.pageSize];
      onUpdate({
        pageSize: newSize,
        customSize: {
          width: currentSize.width,
          height: currentSize.height
        }
      });
    } else {
      onUpdate({ pageSize: newSize });
    }
  };
  
  // Handle orientation change
  const handleOrientationChange = (e) => {
    onUpdate({ orientation: e.target.value });
  };
  
  // Handle custom size change
  const handleCustomSizeChange = (dimension, value) => {
    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue > 0) {
      onUpdate({
        customSize: {
          ...page.customSize,
          [dimension]: numValue
        }
      });
    }
  };
  
  // Handle margin change
  const handleMarginChange = (side, value) => {
    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue >= 0) {
      onUpdate({
        margins: {
          ...page.margins,
          [side]: numValue
        }
      });
    }
  };
  
  // Handle background color change
  const handleBackgroundColorChange = (e) => {
    onUpdate({ backgroundColor: e.target.value });
  };
  
  return (
    <div className="page-settings">
      <h3>Page Settings</h3>
      
      <div className="settings-group">
        <label>Size</label>
        <select value={page.pageSize} onChange={handlePageSizeChange}>
          {Object.entries(PAGE_SIZES).map(([key, { name }]) => (
            <option key={key} value={key}>{name}</option>
          ))}
        </select>
      </div>
      
      {page.pageSize === 'custom' && (
        <div className="settings-group">
          <label>Custom Size (mm)</label>
          <div className="custom-size-inputs">
            <div className="size-input-group">
              <label>Width:</label>
              <input
                type="number"
                min="10"
                max="1000"
                value={page.customSize.width}
                onChange={(e) => handleCustomSizeChange('width', e.target.value)}
                className="size-input"
              />
            </div>
            <div className="size-input-group">
              <label>Height:</label>
              <input
                type="number"
                min="10"
                max="1000"
                value={page.customSize.height}
                onChange={(e) => handleCustomSizeChange('height', e.target.value)}
                className="size-input"
              />
            </div>
          </div>
        </div>
      )}
      
      <div className="settings-group">
        <label>Orientation</label>
        <div className="radio-group">
          <label>
            <input
              type="radio"
              name="orientation"
              value={ORIENTATIONS.PORTRAIT}
              checked={page.orientation === ORIENTATIONS.PORTRAIT}
              onChange={handleOrientationChange}
            /> Portrait
          </label>
          <label>
            <input
              type="radio"
              name="orientation"
              value={ORIENTATIONS.LANDSCAPE}
              checked={page.orientation === ORIENTATIONS.LANDSCAPE}
              onChange={handleOrientationChange}
            /> Landscape
          </label>
        </div>
      </div>
      
      <div className="settings-group">
        <label>Margins (mm)</label>
        <div className="margins-inputs">
          <div className="margin-input-group">
            <label>Top:</label>
            <input
              type="number"
              min="0"
              max="100"
              value={page.margins.top}
              onChange={(e) => handleMarginChange('top', e.target.value)}
              className="margin-input"
            />
          </div>
          <div className="margin-input-group">
            <label>Right:</label>
            <input
              type="number"
              min="0"
              max="100"
              value={page.margins.right}
              onChange={(e) => handleMarginChange('right', e.target.value)}
              className="margin-input"
            />
          </div>
          <div className="margin-input-group">
            <label>Bottom:</label>
            <input
              type="number"
              min="0"
              max="100"
              value={page.margins.bottom}
              onChange={(e) => handleMarginChange('bottom', e.target.value)}
              className="margin-input"
            />
          </div>
          <div className="margin-input-group">
            <label>Left:</label>
            <input
              type="number"
              min="0"
              max="100"
              value={page.margins.left}
              onChange={(e) => handleMarginChange('left', e.target.value)}
              className="margin-input"
            />
          </div>
        </div>
      </div>
      
      <div className="settings-group">
        <label>Background Color</label>
        <input
          type="color"
          value={page.backgroundColor || '#ffffff'}
          onChange={handleBackgroundColorChange}
          className="color-input"
        />
      </div>
    </div>
  );
};

PageSettings.propTypes = {
  page: PropTypes.object.isRequired,
  onUpdate: PropTypes.func.isRequired
};

export default PageSettings;
